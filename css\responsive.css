/* 响应式设计 */

/* 大屏幕 (1200px 及以上) */
@media (min-width: 1200px) {
    .hero-text h1 {
        font-size: 4.2rem;
    }
    
    .section-title h2 {
        font-size: 3.2rem;
    }
    
    .cube-container {
        width: 350px;
        height: 350px;
    }
}

/* 中等屏幕 (992px - 1199px) */
@media (max-width: 1199px) {
    .hero-content {
        max-width: 1000px;
    }
    
    .services-grid {
        max-width: 1000px;
    }
    
    .stats-grid {
        max-width: 1000px;
    }
}

/* 平板设备 (768px - 991px) */
@media (max-width: 991px) {
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 3rem;
    }
    
    .hero-text {
        margin-bottom: 2rem;
    }
    
    .hero-text h1 {
        font-size: 3.2rem;
    }
    
    .cube-container {
        width: 250px;
        height: 250px;
        margin: 0 auto;
    }
    
    .services {
        padding: 6rem 5%;
    }
    
    .services-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 2rem;
    }
    
    .stats {
        padding: 4rem 5%;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
    }
    
    .footer-content {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 2rem;
    }
}

/* 小屏幕设备 (576px - 767px) */
@media (max-width: 767px) {
    .hero {
        padding: 0 3%;
        padding-top: 30px;
    }
    
    .hero-text h1 {
        font-size: 2.8rem;
        letter-spacing: 2px;
    }
    
    .hero-text p {
        font-size: 1.1rem;
        margin-bottom: 2rem;
    }
    
    .section-title h2 {
        font-size: 2.2rem;
        letter-spacing: 2px;
    }
    
    .section-title p {
        font-size: 1rem;
    }
    
    .service-card {
        padding: 2rem;
    }
    
    .service-card h3 {
        font-size: 1.4rem;
    }
    
    .service-icon {
        font-size: 2.5rem;
    }
    
    .stat-item {
        padding: 2rem;
    }
    
    .stat-number {
        font-size: 3rem;
    }
    
    .cube-container {
        width: 200px;
        height: 200px;
    }
    
    .services {
        padding: 5rem 3%;
    }
    
    .stats {
        padding: 3rem 3%;
    }
    
    footer {
        padding: 3rem 3% 1.5rem;
    }
    
    .footer-column h3 {
        font-size: 1.2rem;
    }
    
    .social-links {
        justify-content: center;
    }
}

/* 超小屏幕设备 (480px 及以下) */
@media (max-width: 479px) {
    .hero {
        padding: 0 2%;
        padding-top: 20px;
    }
    
    .hero-text h1 {
        font-size: 2.2rem;
        letter-spacing: 1px;
    }
    
    .hero-text p {
        font-size: 1rem;
        line-height: 1.6;
    }
    
    .section-title h2 {
        font-size: 1.8rem;
        letter-spacing: 1px;
    }
    
    .service-card {
        padding: 1.8rem;
    }
    
    .service-card h3 {
        font-size: 1.3rem;
    }
    
    .service-card p {
        font-size: 0.95rem;
    }
    
    .service-icon {
        font-size: 2.2rem;
    }
    
    .stat-item {
        padding: 1.8rem;
    }
    
    .stat-number {
        font-size: 2.5rem;
    }
    
    .stat-label {
        font-size: 1rem;
    }
    
    .cube-container {
        width: 180px;
        height: 180px;
    }
    
    .cta-button {
        padding: 0.7rem 1.5rem;
        font-size: 0.9rem;
    }
    
    .services {
        padding: 4rem 2%;
    }
    
    .stats {
        padding: 2.5rem 2%;
    }
    
    footer {
        padding: 2.5rem 2% 1rem;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    
    .footer-column h3 {
        font-size: 1.1rem;
    }
    
    .footer-column p {
        font-size: 0.9rem;
    }
    
    .footer-bottom {
        font-size: 0.8rem;
        margin-top: 2rem;
    }
}

/* 横屏模式优化 */
@media (max-height: 600px) and (orientation: landscape) {
    .hero {
        min-height: auto;
        padding-top: 20px;
        padding-bottom: 20px;
    }
    
    .hero-text h1 {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }
    
    .hero-text p {
        margin-bottom: 1.5rem;
    }
    
    .cube-container {
        width: 200px;
        height: 200px;
    }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .cube-face {
        border-width: 0.5px;
    }
    
    .service-card {
        border-width: 0.5px;
    }
    
    .stat-item {
        border-width: 0.5px;
    }
}

/* 打印样式 */
@media print {
    body::before,
    #particle-canvas {
        display: none;
    }
    
    .hero {
        min-height: auto;
        page-break-after: always;
    }
    
    .service-card,
    .stat-item {
        page-break-inside: avoid;
    }
    
    .cta-button {
        display: none;
    }
    
    .social-links {
        display: none;
    }
}
