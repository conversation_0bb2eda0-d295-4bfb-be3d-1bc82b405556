/* 动画定义 */

/* 3D立方体旋转动画 */
@keyframes rotate {
    0% {
        transform: rotateX(0) rotateY(0) rotateZ(0);
    }
    100% {
        transform: rotateX(360deg) rotateY(360deg) rotateZ(360deg);
    }
}

/* 浮动动画 */
@keyframes float {
    0% {
        transform: translate(0, 0);
    }
    50% {
        transform: translate(0, -20px);
    }
    100% {
        transform: translate(0, 0);
    }
}

/* 基础渐入动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 基础动画类 */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

/* 滚动触发的动画 */
.scroll-reveal {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.scroll-reveal.visible {
    opacity: 1;
    transform: translateY(0);
}

/* 波纹效果 */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(0, 238, 255, 0.3);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* 加载状态 */
.loading {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.loaded .loading {
    opacity: 1;
}
