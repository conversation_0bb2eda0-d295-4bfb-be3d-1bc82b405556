/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* CSS变量定义 */
:root {
    --primary: #000000;
    --secondary: #0a0a0a;
    --accent: #00eeff;
    --accent2: #7700ff;
    --text: #ffffff;
    --glass: rgba(30, 30, 40, 0.1);
    --glow: 0 0 15px rgba(0, 238, 255, 0.7);
    --grid-color: rgba(0, 238, 255, 0.05);
}

/* 基础body样式 */
body {
    background: var(--primary);
    color: var(--text);
    font-family: 'Exo 2', sans-serif;
    line-height: 1.6;
    overflow-x: hidden;
    position: relative;
    min-height: 100vh;
}

/* 增强网格背景 */
body::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(var(--grid-color) 1px, transparent 1px),
        linear-gradient(90deg, var(--grid-color) 1px, transparent 1px),
        radial-gradient(circle at 20% 50%, rgba(0, 238, 255, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(119, 0, 255, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(0, 238, 255, 0.02) 0%, transparent 50%);
    background-size: 30px 30px, 30px 30px, 800px 800px, 600px 600px, 400px 400px;
    z-index: -2;
    animation: backgroundShift 60s ease-in-out infinite;
}

@keyframes backgroundShift {
    0%, 100% {
        background-position: 0 0, 0 0, 0% 0%, 100% 0%, 50% 100%;
    }
    50% {
        background-position: 15px 15px, 15px 15px, 100% 100%, 0% 100%, 0% 0%;
    }
}

/* 粒子背景 */
#particle-canvas {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

/* 按钮样式 */
.cta-button {
    background: transparent;
    color: var(--accent);
    border: 1px solid var(--accent);
    padding: 0.8rem 1.8rem;
    border-radius: 0;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--glow);
    letter-spacing: 1px;
    font-family: 'Orbitron', sans-serif;
    position: relative;
    overflow: hidden;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 238, 255, 0.3), transparent);
    transition: 0.5s;
}

.cta-button:hover {
    background: rgba(0, 238, 255, 0.1);
    box-shadow: 0 0 25px rgba(0, 238, 255, 0.5);
}

.cta-button:hover::before {
    left: 100%;
}

/* 主区域样式 */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 0 5%;
    padding-top: 50px; /* 减少顶部padding，因为没有导航栏了 */
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, rgba(119, 0, 255, 0.1) 0%, transparent 70%);
    z-index: -1;
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    align-items: center;
}

.hero-text h1 {
    font-family: 'Orbitron', sans-serif;
    font-size: 3.8rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    text-transform: uppercase;
    letter-spacing: 3px;
}

.hero-text h1 span {
    background: linear-gradient(45deg, var(--accent), var(--accent2));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    display: block;
}

.hero-text p {
    font-size: 1.2rem;
    margin-bottom: 2.5rem;
    opacity: 0.8;
    max-width: 600px;
    line-height: 1.8;
}

.hero-image {
    position: relative;
    display: flex;
    justify-content: center;
    perspective: 1000px;
}

/* 3D立方体样式 */
.cube-container {
    width: 300px;
    height: 300px;
    position: relative;
    transform-style: preserve-3d;
    cursor: pointer;
}

.cube-face {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 1px solid var(--accent);
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    box-shadow:
        inset 0 0 20px rgba(0, 238, 255, 0.1),
        0 0 20px rgba(0, 238, 255, 0.05);
}

.cube-face::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg,
        transparent 30%,
        rgba(0, 238, 255, 0.05) 50%,
        rgba(119, 0, 255, 0.05) 70%,
        transparent 100%);
    opacity: 0.8;
}

.cube-face.front {
    transform: translateZ(150px);
}
.cube-face.back {
    transform: rotateY(180deg) translateZ(150px);
}
.cube-face.right {
    transform: rotateY(90deg) translateZ(150px);
}
.cube-face.left {
    transform: rotateY(-90deg) translateZ(150px);
}
.cube-face.top {
    transform: rotateX(90deg) translateZ(150px);
}
.cube-face.bottom {
    transform: rotateX(-90deg) translateZ(150px);
}

.inner-cube {
    position: absolute;
    width: 150px;
    height: 150px;
    transform-style: preserve-3d;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.inner-cube .cube-face {
    border: 1px solid var(--accent2);
    background: rgba(119, 0, 255, 0.1);
    box-shadow:
        inset 0 0 15px rgba(119, 0, 255, 0.2),
        0 0 15px rgba(119, 0, 255, 0.1);
}

.inner-cube .cube-face.front {
    transform: translateZ(75px);
}
.inner-cube .cube-face.back {
    transform: rotateY(180deg) translateZ(75px);
}
.inner-cube .cube-face.right {
    transform: rotateY(90deg) translateZ(75px);
}
.inner-cube .cube-face.left {
    transform: rotateY(-90deg) translateZ(75px);
}
.inner-cube .cube-face.top {
    transform: rotateX(90deg) translateZ(75px);
}
.inner-cube .cube-face.bottom {
    transform: rotateX(-90deg) translateZ(75px);
}

/* 服务部分 */
.services {
    padding: 8rem 5%;
    position: relative;
}

.section-title {
    text-align: center;
    margin-bottom: 5rem;
}

.section-title h2 {
    font-family: 'Orbitron', sans-serif;
    font-size: 2.8rem;
    margin-bottom: 1.5rem;
    text-transform: uppercase;
    letter-spacing: 3px;
}

.section-title h2 span {
    background: linear-gradient(45deg, var(--accent), var(--accent2));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.section-title p {
    max-width: 700px;
    margin: 0 auto;
    font-size: 1.1rem;
    opacity: 0.8;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2.5rem;
    max-width: 1200px;
    margin: 0 auto;
}

.service-card {
    background: rgba(10, 10, 10, 0.7);
    border-radius: 0;
    padding: 2.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid var(--accent);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    transform-style: preserve-3d;
    transform: perspective(1000px);
}

.service-card::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, var(--accent), var(--accent2), var(--accent));
    z-index: -1;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.service-card:hover {
    transform: translateY(-10px) translateZ(20px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.service-card:hover::before {
    opacity: 1;
}

.service-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    background: linear-gradient(45deg, var(--accent), var(--accent2));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.service-card h3 {
    font-size: 1.6rem;
    margin-bottom: 1.2rem;
    font-weight: 600;
    font-family: 'Orbitron', sans-serif;
    letter-spacing: 1px;
}

.service-card p {
    opacity: 0.8;
    margin-bottom: 1.8rem;
    line-height: 1.8;
}

/* 统计部分 */
.stats {
    padding: 5rem 5%;
    position: relative;
    overflow: hidden;
}

.stats::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent, rgba(119, 0, 255, 0.05), transparent);
    z-index: -1;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.stat-item {
    text-align: center;
    padding: 2.5rem;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 0;
    backdrop-filter: blur(10px);
    border: 1px solid var(--accent);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 238, 255, 0.2);
}

.stat-number {
    font-family: 'Orbitron', sans-serif;
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, var(--accent), var(--accent2));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.stat-label {
    font-size: 1.1rem;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* 页脚 */
footer {
    background: #050505;
    padding: 4rem 5% 2rem;
    position: relative;
    border-top: 3px solid;
    border-image: linear-gradient(90deg, var(--accent), var(--accent2), var(--accent)) 1;
}

footer::before {
    content: '';
    position: absolute;
    top: -1px;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--accent), var(--accent2), var(--accent), transparent);
    animation: glow-line 3s ease-in-out infinite alternate;
}

@keyframes glow-line {
    0% {
        opacity: 0.5;
        box-shadow: 0 0 5px var(--accent);
    }
    100% {
        opacity: 1;
        box-shadow: 0 0 20px var(--accent), 0 0 30px var(--accent2);
    }
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
}

.footer-column h3 {
    font-size: 1.4rem;
    margin-bottom: 1.8rem;
    position: relative;
    display: inline-block;
    font-family: 'Orbitron', sans-serif;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.footer-column h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 50px;
    height: 2px;
    background: linear-gradient(90deg, var(--accent), var(--accent2));
}

.footer-column p {
    opacity: 0.7;
    margin-bottom: 1.2rem;
    line-height: 1.8;
}

.social-links {
    display: flex;
    gap: 1.2rem;
    margin-top: 2rem;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    border-radius: 0;
    background: rgba(10, 10, 10, 0.8);
    color: var(--accent);
    transition: all 0.3s ease;
    border: 1px solid var(--accent);
    font-size: 1.2rem;
}

.social-links a:hover {
    background: linear-gradient(45deg, var(--accent), var(--accent2));
    color: black;
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 238, 255, 0.4);
}

.footer-bottom {
    max-width: 1200px;
    margin: 3rem auto 0;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    text-align: center;
    opacity: 0.7;
    font-size: 0.9rem;
    letter-spacing: 1px;
    position: relative;
}

.footer-bottom::before {
    content: '';
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 2px;
    background: linear-gradient(90deg,
        transparent 0%,
        var(--accent) 25%,
        var(--accent2) 50%,
        var(--accent) 75%,
        transparent 100%);
    animation: bottomGlow 4s ease-in-out infinite alternate;
}

@keyframes bottomGlow {
    0% {
        opacity: 0.3;
        box-shadow: 0 0 10px var(--accent);
    }
    100% {
        opacity: 0.8;
        box-shadow: 0 0 25px var(--accent), 0 0 35px var(--accent2);
    }
}

/* 页面底部额外装饰 */
body::after {
    content: '';
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(0, 238, 255, 0.3) 20%,
        rgba(119, 0, 255, 0.3) 50%,
        rgba(0, 238, 255, 0.3) 80%,
        transparent 100%);
    z-index: 1000;
    animation: bottomBorder 6s ease-in-out infinite;
}

@keyframes bottomBorder {
    0%, 100% {
        opacity: 0.2;
    }
    50% {
        opacity: 0.6;
    }
}
