// 主JavaScript文件

// 粒子背景初始化
document.addEventListener('DOMContentLoaded', function() {
    initParticleBackground();
    initScrollAnimations();
    initButtonEffects();
    init3DCubeInteraction();
});

// 粒子背景系统
function initParticleBackground() {
    const canvas = document.getElementById('particle-canvas');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    // 设置画布尺寸
    function resizeCanvas() {
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
    }

    resizeCanvas();

    // 粒子数组
    const particles = [];
    const particleCount = 100; // 减少粒子数量以提高性能
    
    // 粒子类
    class Particle {
        constructor() {
            this.x = Math.random() * canvas.width;
            this.y = Math.random() * canvas.height;
            this.radius = Math.random() * 1.5 + 0.5;
            this.speedX = (Math.random() - 0.5) * 0.3;
            this.speedY = (Math.random() - 0.5) * 0.3;
            this.color = `rgba(0, 238, 255, ${Math.random() * 0.3 + 0.1})`;
        }
        
        update() {
            this.x += this.speedX;
            this.y += this.speedY;
            
            // 边界检查
            if (this.x < 0) this.x = canvas.width;
            if (this.x > canvas.width) this.x = 0;
            if (this.y < 0) this.y = canvas.height;
            if (this.y > canvas.height) this.y = 0;
        }
        
        draw() {
            ctx.beginPath();
            ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
            ctx.fillStyle = this.color;
            ctx.fill();
        }
    }
    
    // 创建粒子
    for (let i = 0; i < particleCount; i++) {
        particles.push(new Particle());
    }
    
    // 绘制连线
    function drawConnections() {
        for (let i = 0; i < particles.length; i++) {
            for (let j = i + 1; j < particles.length; j++) {
                const distance = Math.sqrt(
                    Math.pow(particles[i].x - particles[j].x, 2) + 
                    Math.pow(particles[i].y - particles[j].y, 2)
                );
                
                if (distance < 100) {
                    ctx.beginPath();
                    ctx.moveTo(particles[i].x, particles[i].y);
                    ctx.lineTo(particles[j].x, particles[j].y);
                    ctx.strokeStyle = `rgba(0, 238, 255, ${0.1 * (1 - distance/100)})`;
                    ctx.lineWidth = 0.2;
                    ctx.stroke();
                }
            }
        }
    }
    
    // 动画循环
    function animate() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // 更新和绘制粒子
        particles.forEach(particle => {
            particle.update();
            particle.draw();
        });
        
        // 绘制连线
        drawConnections();
        
        requestAnimationFrame(animate);
    }
    
    animate();
    
    // 窗口大小调整
    window.addEventListener('resize', resizeCanvas);
}

// 滚动动画系统
function initScrollAnimations() {
    const observerOptions = {
        root: null,
        rootMargin: '0px',
        threshold: 0.1
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
                
                // 为统计数字添加计数动画
                if (entry.target.classList.contains('stat-item')) {
                    animateStatNumber(entry.target);
                }
            }
        });
    }, observerOptions);
    
    // 观察需要动画的元素
    document.querySelectorAll('.service-card, .stat-item').forEach(item => {
        item.classList.add('scroll-reveal');
        observer.observe(item);
    });
}

// 统计数字动画
function animateStatNumber(statItem) {
    const numberElement = statItem.querySelector('.stat-number');
    if (!numberElement) return;
    
    const finalNumber = numberElement.textContent;
    const isPercentage = finalNumber.includes('%');
    const isPlus = finalNumber.includes('+');
    const numericValue = parseFloat(finalNumber.replace(/[^\d.]/g, ''));
    
    let currentNumber = 0;
    const increment = numericValue / 60; // 60帧动画
    const duration = 2000; // 2秒
    const frameRate = 1000 / 60;
    
    const timer = setInterval(() => {
        currentNumber += increment;
        
        if (currentNumber >= numericValue) {
            currentNumber = numericValue;
            clearInterval(timer);
        }
        
        let displayNumber = Math.floor(currentNumber);
        if (isPercentage) {
            displayNumber = (currentNumber).toFixed(2);
        }
        
        numberElement.textContent = displayNumber + (isPercentage ? '%' : '') + (isPlus ? '+' : '');
    }, frameRate);
}

// 按钮效果
function initButtonEffects() {
    const buttons = document.querySelectorAll('.cta-button');
    
    buttons.forEach(button => {
        // 点击波纹效果
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
        
        // 悬停效果
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
}

// 平滑滚动
function smoothScroll(target) {
    const element = document.querySelector(target);
    if (element) {
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// 平滑滚动功能
function smoothScroll(target) {
    const element = document.querySelector(target);
    if (element) {
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// 3D立方体鼠标跟随旋转 - 自动旋转 + 鼠标跟随
function init3DCubeInteraction() {
    const cubeContainer = document.querySelector('.cube-container');
    if (!cubeContainer) return;

    let mouseX = 0;
    let mouseY = 0;
    let isHovering = false;
    let animationStartTime = Date.now();

    // 动画循环函数
    function animate() {
        const currentTime = Date.now();
        const elapsed = currentTime - animationStartTime;

        // 计算自动旋转角度 (20秒一圈)
        const autoRotationSpeed = 360 / 20000; // 度/毫秒
        const autoRotateX = (elapsed * autoRotationSpeed) % 360;
        const autoRotateY = (elapsed * autoRotationSpeed) % 360;
        const autoRotateZ = (elapsed * autoRotationSpeed) % 360;

        // 计算鼠标跟随旋转角度
        const mouseRotateX = isHovering ? -mouseY * 30 : 0;
        const mouseRotateY = isHovering ? mouseX * 30 : 0;

        // 组合旋转：自动旋转 + 鼠标跟随
        const finalRotateX = autoRotateX + mouseRotateX;
        const finalRotateY = autoRotateY + mouseRotateY;
        const finalRotateZ = autoRotateZ;

        // 应用变换
        cubeContainer.style.transform = `rotateX(${finalRotateX}deg) rotateY(${finalRotateY}deg) rotateZ(${finalRotateZ}deg)`;

        requestAnimationFrame(animate);
    }

    // 开始动画循环
    animate();

    // 鼠标事件
    cubeContainer.addEventListener('mouseenter', function() {
        isHovering = true;
    });

    cubeContainer.addEventListener('mouseleave', function() {
        isHovering = false;
        mouseX = 0;
        mouseY = 0;
    });

    cubeContainer.addEventListener('mousemove', function(e) {
        if (!isHovering) return;

        const rect = this.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        // 计算鼠标相对位置 (-1 到 1)
        mouseX = (e.clientX - centerX) / (rect.width / 2);
        mouseY = (e.clientY - centerY) / (rect.height / 2);

        // 限制范围
        mouseX = Math.max(-1, Math.min(1, mouseX));
        mouseY = Math.max(-1, Math.min(1, mouseY));
    });

    // 触摸设备支持
    cubeContainer.addEventListener('touchstart', function(e) {
        isHovering = true;
        e.preventDefault();
    });

    cubeContainer.addEventListener('touchend', function() {
        isHovering = false;
        mouseX = 0;
        mouseY = 0;
    });

    cubeContainer.addEventListener('touchmove', function(e) {
        if (!isHovering) return;

        const touch = e.touches[0];
        const rect = this.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        mouseX = (touch.clientX - centerX) / (rect.width / 2);
        mouseY = (touch.clientY - centerY) / (rect.height / 2);

        mouseX = Math.max(-1, Math.min(1, mouseX));
        mouseY = Math.max(-1, Math.min(1, mouseY));

        e.preventDefault();
    });
}

// 导出主要功能
window.NEXUS = {
    smoothScroll
};
