/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary: #000000;
    --secondary: #0a0a0a;
    --accent: #00eeff;
    --accent2: #7700ff;
    --text: #ffffff;
    --glass: rgba(30, 30, 40, 0.1);
    --glow: 0 0 15px rgba(0, 238, 255, 0.7);
    --grid-color: rgba(0, 238, 255, 0.05);
}

body {
    background: var(--primary);
    color: var(--text);
    font-family: 'Exo 2', sans-serif;
    line-height: 1.6;
    overflow-x: hidden;
    position: relative;
    min-height: 100vh;
}

/* 网格背景 */
body::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(var(--grid-color) 1px, transparent 1px),
        linear-gradient(90deg, var(--grid-color) 1px, transparent 1px);
    background-size: 30px 30px;
    z-index: -2;
}

/* 粒子背景 */
#particle-canvas {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

/* 导航栏样式 */
header {
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    padding: 1.5rem 5%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    backdrop-filter: blur(10px);
    background: rgba(0, 0, 0, 0.7);
    border-bottom: 1px solid rgba(0, 238, 255, 0.1);
}

.logo {
    font-family: 'Orbitron', sans-serif;
    font-size: 2rem;
    font-weight: 800;
    background: linear-gradient(45deg, var(--accent), var(--accent2));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    letter-spacing: 2px;
    text-transform: uppercase;
}

.nav-links {
    display: none;
}

.cta-button {
    background: transparent;
    color: var(--accent);
    border: 1px solid var(--accent);
    padding: 0.8rem 1.8rem;
    border-radius: 0;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--glow);
    letter-spacing: 1px;
    font-family: 'Orbitron', sans-serif;
    position: relative;
    overflow: hidden;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 238, 255, 0.3), transparent);
    transition: 0.5s;
}

.cta-button:hover {
    background: rgba(0, 238, 255, 0.1);
    box-shadow: 0 0 25px rgba(0, 238, 255, 0.5);
}

.cta-button:hover::before {
    left: 100%;
}

/* 主区域样式 */
.hero {
    min-height: 80vh;
    display: flex;
    align-items: center;
    padding: 3rem 5%;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120%;
    height: 120%;
    background: radial-gradient(
        circle at center,
        rgba(119, 0, 255, 0.15) 0%,
        rgba(0, 238, 255, 0.1) 30%,
        transparent 70%
    );
    z-index: -1;
    animation: pulse-bg 8s ease-in-out infinite;
}

@keyframes pulse-bg {
    0%, 100% {
        opacity: 0.8;
        transform: translate(-50%, -50%) scale(1);
    }
    50% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.1);
    }
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    align-items: center;
}

.hero-text h1 {
    font-family: 'Orbitron', sans-serif;
    font-size: 4.2rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 2rem;
    text-transform: uppercase;
    letter-spacing: 4px;
    text-shadow: 0 0 20px rgba(0, 238, 255, 0.3);
}

.hero-text h1 span {
    background: linear-gradient(45deg, var(--accent), var(--accent2));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    display: block;
}

.hero-text p {
    font-size: 1.3rem;
    margin-bottom: 3rem;
    opacity: 0.9;
    max-width: 650px;
    line-height: 1.9;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
}

.hero-image {
    position: relative;
    display: flex;
    justify-content: center;
    perspective: 1000px;
}

.cube-container {
    width: 350px;
    height: 350px;
    position: relative;
    transform-style: preserve-3d;
    animation: rotate 25s infinite linear;
    cursor: grab;
    transition: transform 0.05s ease-out;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    touch-action: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
}

.cube-container:active {
    cursor: grabbing;
}

.cube-container.dragging {
    animation-play-state: paused;
    transition: none;
}

.cube-face {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 2px solid var(--accent);
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    box-shadow:
        inset 0 0 20px rgba(0, 238, 255, 0.1),
        0 0 20px rgba(0, 238, 255, 0.2);
}

.cube-face::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        transparent 30%,
        rgba(0, 238, 255, 0.15) 50%,
        rgba(119, 0, 255, 0.1) 70%,
        transparent 100%
    );
    opacity: 0.8;
}

.cube-face.front {
    transform: translateZ(175px);
}
.cube-face.back {
    transform: rotateY(180deg) translateZ(175px);
}
.cube-face.right {
    transform: rotateY(90deg) translateZ(175px);
}
.cube-face.left {
    transform: rotateY(-90deg) translateZ(175px);
}
.cube-face.top {
    transform: rotateX(90deg) translateZ(175px);
}
.cube-face.bottom {
    transform: rotateX(-90deg) translateZ(175px);
}

.inner-cube {
    position: absolute;
    width: 175px;
    height: 175px;
    transform-style: preserve-3d;
    animation: rotate 18s infinite linear reverse;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.inner-cube .cube-face {
    border: 2px solid var(--accent2);
    background: rgba(119, 0, 255, 0.1);
    box-shadow:
        inset 0 0 15px rgba(119, 0, 255, 0.2),
        0 0 15px rgba(119, 0, 255, 0.3);
}

.inner-cube .cube-face.front {
    transform: translateZ(87.5px);
}
.inner-cube .cube-face.back {
    transform: rotateY(180deg) translateZ(87.5px);
}
.inner-cube .cube-face.right {
    transform: rotateY(90deg) translateZ(87.5px);
}
.inner-cube .cube-face.left {
    transform: rotateY(-90deg) translateZ(87.5px);
}
.inner-cube .cube-face.top {
    transform: rotateX(90deg) translateZ(87.5px);
}
.inner-cube .cube-face.bottom {
    transform: rotateX(-90deg) translateZ(87.5px);
}

/* 服务部分 */
.services {
    padding: 5rem 5%;
    position: relative;
}

.section-title {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title h2 {
    font-family: 'Orbitron', sans-serif;
    font-size: 2.8rem;
    margin-bottom: 1.5rem;
    text-transform: uppercase;
    letter-spacing: 3px;
}

.section-title h2 span {
    background: linear-gradient(45deg, var(--accent), var(--accent2));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.section-title p {
    max-width: 700px;
    margin: 0 auto;
    font-size: 1.1rem;
    opacity: 0.8;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2.5rem;
    max-width: 1200px;
    margin: 0 auto;
}

.service-card {
    background: rgba(10, 10, 10, 0.7);
    border-radius: 0;
    padding: 2.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid var(--accent);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    transform-style: preserve-3d;
    transform: perspective(1000px);
}

.service-card::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, var(--accent), var(--accent2), var(--accent));
    z-index: -1;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.service-card:hover {
    transform: translateY(-10px) translateZ(20px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.service-card:hover::before {
    opacity: 1;
}

.service-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    background: linear-gradient(45deg, var(--accent), var(--accent2));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.service-card h3 {
    font-size: 1.6rem;
    margin-bottom: 1.2rem;
    font-weight: 600;
    font-family: 'Orbitron', sans-serif;
    letter-spacing: 1px;
}

.service-card p {
    opacity: 0.8;
    margin-bottom: 1.8rem;
    line-height: 1.8;
}

/* 统计部分 */
.stats {
    padding: 3rem 5%;
    position: relative;
    overflow: hidden;
}

.stats::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent, rgba(119, 0, 255, 0.05), transparent);
    z-index: -1;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.stat-item {
    text-align: center;
    padding: 2.5rem;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 0;
    backdrop-filter: blur(10px);
    border: 1px solid var(--accent);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 238, 255, 0.2);
}

.stat-number {
    font-family: 'Orbitron', sans-serif;
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, var(--accent), var(--accent2));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.stat-label {
    font-size: 1.1rem;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 1px;
}
