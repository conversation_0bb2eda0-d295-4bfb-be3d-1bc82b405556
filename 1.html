<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NEXUS | 未来科技</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800&family=Exo+2:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary: #000000;
            --secondary: #0a0a0a;
            --accent: #00eeff;
            --accent2: #7700ff;
            --text: #ffffff;
            --glass: rgba(30, 30, 40, 0.1);
            --glow: 0 0 15px rgba(0, 238, 255, 0.7);
            --grid-color: rgba(0, 238, 255, 0.05);
        }

        body {
            background: var(--primary);
            color: var(--text);
            font-family: 'Exo 2', sans-serif;
            line-height: 1.6;
            overflow-x: hidden;
            position: relative;
            min-height: 100vh;
        }

        /* 网格背景 */
        body::before {
            content: "";
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(var(--grid-color) 1px, transparent 1px),
                linear-gradient(90deg, var(--grid-color) 1px, transparent 1px);
            background-size: 30px 30px;
            z-index: -2;
        }

        /* 粒子背景 */
        #particle-canvas {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }

        /* 导航栏样式 */
        header {
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            padding: 1.5rem 5%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            backdrop-filter: blur(10px);
            background: rgba(0, 0, 0, 0.7);
            border-bottom: 1px solid rgba(0, 238, 255, 0.1);
        }

        .logo {
            font-family: 'Orbitron', sans-serif;
            font-size: 2rem;
            font-weight: 800;
            background: linear-gradient(45deg, var(--accent), var(--accent2));
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            letter-spacing: 2px;
            text-transform: uppercase;
        }

        .nav-links {
            display: flex;
            gap: 2.5rem;
        }

        .nav-links a {
            color: var(--text);
            text-decoration: none;
            font-weight: 500;
            font-size: 1.1rem;
            position: relative;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-family: 'Orbitron', sans-serif;
        }

        .nav-links a::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--accent), var(--accent2));
            transition: width 0.3s ease;
        }

        .nav-links a:hover {
            color: var(--accent);
        }

        .nav-links a:hover::after {
            width: 100%;
        }

        .cta-button {
            background: transparent;
            color: var(--accent);
            border: 1px solid var(--accent);
            padding: 0.8rem 1.8rem;
            border-radius: 0;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: var(--glow);
            letter-spacing: 1px;
            font-family: 'Orbitron', sans-serif;
            position: relative;
            overflow: hidden;
        }

        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 238, 255, 0.3), transparent);
            transition: 0.5s;
        }

        .cta-button:hover {
            background: rgba(0, 238, 255, 0.1);
            box-shadow: 0 0 25px rgba(0, 238, 255, 0.5);
        }

        .cta-button:hover::before {
            left: 100%;
        }

        /* 主区域样式 */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            padding: 0 5%;
            padding-top: 100px;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at center, rgba(119, 0, 255, 0.1) 0%, transparent 70%);
            z-index: -1;
        }

        .hero-content {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            align-items: center;
        }

        .hero-text h1 {
            font-family: 'Orbitron', sans-serif;
            font-size: 3.8rem;
            font-weight: 800;
            line-height: 1.1;
            margin-bottom: 1.5rem;
            text-transform: uppercase;
            letter-spacing: 3px;
        }

        .hero-text h1 span {
            background: linear-gradient(45deg, var(--accent), var(--accent2));
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            display: block;
        }

        .hero-text p {
            font-size: 1.2rem;
            margin-bottom: 2.5rem;
            opacity: 0.8;
            max-width: 600px;
            line-height: 1.8;
        }

        .hero-image {
            position: relative;
            display: flex;
            justify-content: center;
            perspective: 1000px;
        }

        .cube-container {
            width: 300px;
            height: 300px;
            position: relative;
            transform-style: preserve-3d;
            animation: rotate 20s infinite linear;
        }

        .cube-face {
            position: absolute;
            width: 100%;
            height: 100%;
            border: 1px solid var(--accent);
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
        }

        .cube-face::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent 50%, rgba(0, 238, 255, 0.1) 100%);
        }

        .cube-face.front {
            transform: translateZ(150px);
        }
        .cube-face.back {
            transform: rotateY(180deg) translateZ(150px);
        }
        .cube-face.right {
            transform: rotateY(90deg) translateZ(150px);
        }
        .cube-face.left {
            transform: rotateY(-90deg) translateZ(150px);
        }
        .cube-face.top {
            transform: rotateX(90deg) translateZ(150px);
        }
        .cube-face.bottom {
            transform: rotateX(-90deg) translateZ(150px);
        }

        .inner-cube {
            position: absolute;
            width: 150px;
            height: 150px;
            transform-style: preserve-3d;
            animation: rotate 15s infinite linear reverse;
        }

        .inner-cube .cube-face {
            border: 1px solid var(--accent2);
            background: rgba(119, 0, 255, 0.05);
        }

        .inner-cube .cube-face.front {
            transform: translateZ(75px);
        }
        .inner-cube .cube-face.back {
            transform: rotateY(180deg) translateZ(75px);
        }
        .inner-cube .cube-face.right {
            transform: rotateY(90deg) translateZ(75px);
        }
        .inner-cube .cube-face.left {
            transform: rotateY(-90deg) translateZ(75px);
        }
        .inner-cube .cube-face.top {
            transform: rotateX(90deg) translateZ(75px);
        }
        .inner-cube .cube-face.bottom {
            transform: rotateX(-90deg) translateZ(75px);
        }

        /* 服务部分 */
        .services {
            padding: 8rem 5%;
            position: relative;
        }

        .section-title {
            text-align: center;
            margin-bottom: 5rem;
        }

        .section-title h2 {
            font-family: 'Orbitron', sans-serif;
            font-size: 2.8rem;
            margin-bottom: 1.5rem;
            text-transform: uppercase;
            letter-spacing: 3px;
        }

        .section-title h2 span {
            background: linear-gradient(45deg, var(--accent), var(--accent2));
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .section-title p {
            max-width: 700px;
            margin: 0 auto;
            font-size: 1.1rem;
            opacity: 0.8;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2.5rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .service-card {
            background: rgba(10, 10, 10, 0.7);
            border-radius: 0;
            padding: 2.5rem;
            backdrop-filter: blur(10px);
            border: 1px solid var(--accent);
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
            transform-style: preserve-3d;
            transform: perspective(1000px);
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, var(--accent), var(--accent2), var(--accent));
            z-index: -1;
            opacity: 0;
            transition: opacity 0.4s ease;
        }

        .service-card:hover {
            transform: translateY(-10px) translateZ(20px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
        }

        .service-card:hover::before {
            opacity: 1;
        }

        .service-icon {
            font-size: 3rem;
            margin-bottom: 1.5rem;
            background: linear-gradient(45deg, var(--accent), var(--accent2));
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .service-card h3 {
            font-size: 1.6rem;
            margin-bottom: 1.2rem;
            font-weight: 600;
            font-family: 'Orbitron', sans-serif;
            letter-spacing: 1px;
        }

        .service-card p {
            opacity: 0.8;
            margin-bottom: 1.8rem;
            line-height: 1.8;
        }

        /* 统计部分 */
        .stats {
            padding: 5rem 5%;
            position: relative;
            overflow: hidden;
        }

        .stats::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent, rgba(119, 0, 255, 0.05), transparent);
            z-index: -1;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .stat-item {
            text-align: center;
            padding: 2.5rem;
            background: rgba(0, 0, 0, 0.7);
            border-radius: 0;
            backdrop-filter: blur(10px);
            border: 1px solid var(--accent);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 238, 255, 0.2);
        }

        .stat-number {
            font-family: 'Orbitron', sans-serif;
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            background: linear-gradient(45deg, var(--accent), var(--accent2));
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .stat-label {
            font-size: 1.1rem;
            opacity: 0.8;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* 页脚 */
        footer {
            background: #050505;
            padding: 4rem 5% 2rem;
            position: relative;
            border-top: 1px solid rgba(0, 238, 255, 0.1);
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 3rem;
        }

        .footer-column h3 {
            font-size: 1.4rem;
            margin-bottom: 1.8rem;
            position: relative;
            display: inline-block;
            font-family: 'Orbitron', sans-serif;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .footer-column h3::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 0;
            width: 50px;
            height: 2px;
            background: linear-gradient(90deg, var(--accent), var(--accent2));
        }

        .footer-column p {
            opacity: 0.7;
            margin-bottom: 1.2rem;
            line-height: 1.8;
        }

        .social-links {
            display: flex;
            gap: 1.2rem;
            margin-top: 2rem;
        }

        .social-links a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 45px;
            height: 45px;
            border-radius: 0;
            background: rgba(10, 10, 10, 0.8);
            color: var(--accent);
            transition: all 0.3s ease;
            border: 1px solid var(--accent);
            font-size: 1.2rem;
        }

        .social-links a:hover {
            background: linear-gradient(45deg, var(--accent), var(--accent2));
            color: black;
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 238, 255, 0.4);
        }

        .footer-bottom {
            max-width: 1200px;
            margin: 3rem auto 0;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.05);
            text-align: center;
            opacity: 0.7;
            font-size: 0.9rem;
            letter-spacing: 1px;
        }

        /* 动画 */
        @keyframes rotate {
            0% {
                transform: rotateX(0) rotateY(0) rotateZ(0);
            }
            100% {
                transform: rotateX(360deg) rotateY(360deg) rotateZ(360deg);
            }
        }

        @keyframes float {
            0% {
                transform: translate(0, 0);
            }
            50% {
                transform: translate(0, -20px);
            }
            100% {
                transform: translate(0, 0);
            }
        }

        /* 响应式设计 */
        @media (max-width: 992px) {
            .hero-content {
                grid-template-columns: 1fr;
                text-align: center;
            }
            
            .hero-text {
                margin-bottom: 3rem;
            }
            
            .cube-container {
                width: 250px;
                height: 250px;
                margin: 0 auto;
            }
            
            .nav-links {
                display: none;
            }
        }

        @media (max-width: 768px) {
            .hero-text h1 {
                font-size: 2.8rem;
            }
            
            .section-title h2 {
                font-size: 2.2rem;
            }
            
            .service-card {
                padding: 2rem;
            }
            
            .stat-item {
                padding: 2rem;
            }
        }

        @media (max-width: 480px) {
            .hero-text h1 {
                font-size: 2.2rem;
            }
            
            .section-title h2 {
                font-size: 1.8rem;
            }
            
            .service-card {
                padding: 1.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- 粒子背景 -->
    <div id="particle-canvas"></div>
    
    <!-- 导航栏 -->
    <header>
        <div class="logo">NEXUS</div>
        <nav class="nav-links">
            <a href="#home">首页</a>
            <a href="#services">服务</a>
            <a href="#about">技术</a>
            <a href="#products">产品</a>
            <a href="#contact">联系</a>
        </nav>
        <button class="cta-button">进入系统</button>
    </header>
    
    <!-- 主区域 -->
    <section class="hero" id="home">
        <div class="hero-content">
            <div class="hero-text">
                <h1><span>未来科技</span>重塑数字世界</h1>
                <p>在NEXUS，我们致力于通过尖端技术解决方案推动人类进步。融合人工智能、量子计算与神经科学，打造前所未有的数字体验。</p>
                <button class="cta-button">探索未来</button>
            </div>
            <div class="hero-image">
                <div class="cube-container">
                    <div class="cube-face front"></div>
                    <div class="cube-face back"></div>
                    <div class="cube-face right"></div>
                    <div class="cube-face left"></div>
                    <div class="cube-face top"></div>
                    <div class="cube-face bottom"></div>
                    <div class="inner-cube">
                        <div class="cube-face front"></div>
                        <div class="cube-face back"></div>
                        <div class="cube-face right"></div>
                        <div class="cube-face left"></div>
                        <div class="cube-face top"></div>
                        <div class="cube-face bottom"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- 服务部分 -->
    <section class="services" id="services">
        <div class="section-title">
            <h2>核心<span>解决方案</span></h2>
            <p>我们提供革命性的技术服务，推动企业进入数字新时代</p>
        </div>
        
        <div class="services-grid">
            <div class="service-card">
                <div class="service-icon">
                    <i class="fas fa-brain"></i>
                </div>
                <h3>量子智能系统</h3>
                <p>融合量子计算与深度学习的下一代AI系统，解决传统计算无法处理的复杂问题，实现指数级性能提升。</p>
                <button class="cta-button">了解技术</button>
            </div>
            
            <div class="service-card">
                <div class="service-icon">
                    <i class="fas fa-atom"></i>
                </div>
                <h3>神经接口技术</h3>
                <p>基于先进脑机接口的人机交互系统，实现思维控制设备，革新用户体验与生产力模式。</p>
                <button class="cta-button">了解技术</button>
            </div>
            
            <div class="service-card">
                <div class="service-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h3>区块链安全网络</h3>
                <p>量子抗性区块链架构，提供无法破解的数据安全与交易透明度，保护数字资产免受未来威胁。</p>
                <button class="cta-button">了解技术</button>
            </div>
        </div>
    </section>
    
    <!-- 统计部分 -->
    <section class="stats">
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-number">15+</div>
                <div class="stat-label">核心技术专利</div>
            </div>
            
            <div class="stat-item">
                <div class="stat-number">99.99%</div>
                <div class="stat-label">系统稳定性</div>
            </div>
            
            <div class="stat-item">
                <div class="stat-number">2000+</div>
                <div class="stat-label">企业客户</div>
            </div>
            
            <div class="stat-item">
                <div class="stat-number">0</div>
                <div class="stat-label">安全漏洞</div>
            </div>
        </div>
    </section>
    
    <!-- 页脚 -->
    <footer>
        <div class="footer-content">
            <div class="footer-column">
                <h3>关于NEXUS</h3>
                <p>我们站在科技前沿，致力于通过创新技术解决人类面临的复杂挑战，创造更智能、更安全的数字未来。</p>
                <div class="social-links">
                    <a href="#"><i class="fab fa-github"></i></a>
                    <a href="#"><i class="fab fa-linkedin-in"></i></a>
                    <a href="#"><i class="fab fa-twitter"></i></a>
                    <a href="#"><i class="fab fa-discord"></i></a>
                </div>
            </div>
            
            <div class="footer-column">
                <h3>技术领域</h3>
                <p>量子计算</p>
                <p>神经科学</p>
                <p>区块链安全</p>
                <p>人工智能</p>
                <p>增强现实</p>
            </div>
            
            <div class="footer-column">
                <h3>联系我们</h3>
                <p><i class="fas fa-map-marker-alt"></i> 北京市海淀区科技园88号</p>
                <p><i class="fas fa-phone"></i> +86 10 8888 7777</p>
                <p><i class="fas fa-envelope"></i> <EMAIL></p>
                <p><i class="fas fa-globe"></i> www.nexus.tech</p>
            </div>
        </div>
        
        <div class="footer-bottom">
            <p>&copy; 2025 NEXUS TECHNOLOGIES | 深度求索量子计算实验室</p>
        </div>
    </footer>
    
    <script>
        // 粒子背景初始化
        document.addEventListener('DOMContentLoaded', function() {
            const canvas = document.getElementById('particle-canvas');
            const ctx = canvas.getContext('2d');
            
            // 设置画布尺寸
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            
            // 粒子数组
            const particles = [];
            const particleCount = 200;
            
            // 创建粒子
            for (let i = 0; i < particleCount; i++) {
                particles.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    radius: Math.random() * 1.5 + 0.5,
                    speedX: (Math.random() - 0.5) * 0.3,
                    speedY: (Math.random() - 0.5) * 0.3,
                    color: `rgba(0, 238, 255, ${Math.random() * 0.3 + 0.1})`
                });
            }
            
            // 绘制粒子
            function drawParticles() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                particles.forEach(particle => {
                    ctx.beginPath();
                    ctx.arc(particle.x, particle.y, particle.radius, 0, Math.PI * 2);
                    ctx.fillStyle = particle.color;
                    ctx.fill();
                    
                    // 更新位置
                    particle.x += particle.speedX;
                    particle.y += particle.speedY;
                    
                    // 边界检查
                    if (particle.x < 0) particle.x = canvas.width;
                    if (particle.x > canvas.width) particle.x = 0;
                    if (particle.y < 0) particle.y = canvas.height;
                    if (particle.y > canvas.height) particle.y = 0;
                    
                    // 绘制粒子间的连线
                    particles.forEach(otherParticle => {
                        const distance = Math.sqrt(
                            Math.pow(particle.x - otherParticle.x, 2) + 
                            Math.pow(particle.y - otherParticle.y, 2)
                        );
                        
                        if (distance < 100) {
                            ctx.beginPath();
                            ctx.moveTo(particle.x, particle.y);
                            ctx.lineTo(otherParticle.x, otherParticle.y);
                            ctx.strokeStyle = `rgba(0, 238, 255, ${0.1 * (1 - distance/100)})`;
                            ctx.lineWidth = 0.2;
                            ctx.stroke();
                        }
                    });
                });
                
                requestAnimationFrame(drawParticles);
            }
            
            drawParticles();
            
            // 窗口大小调整
            window.addEventListener('resize', function() {
                canvas.width = window.innerWidth;
                canvas.height = window.innerHeight;
            });
            
            // 滚动动画效果
            const observerOptions = {
                root: null,
                rootMargin: '0px',
                threshold: 0.1
            };
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                    }
                });
            }, observerOptions);
            
            // 观察需要动画的元素
            document.querySelectorAll('.service-card, .stat-item').forEach(item => {
                observer.observe(item);
            });
        });
    </script>
</body>
</html>  